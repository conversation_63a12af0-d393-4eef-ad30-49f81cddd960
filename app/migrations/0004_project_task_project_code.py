# Generated by Django 5.0.4 on 2025-08-07 21:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0003_alter_tasksample_code'),
    ]

    operations = [
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.IntegerField(verbose_name='创建用户ID')),
                ('username', models.CharField(max_length=100, verbose_name='创建用户名')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='项目编号')),
                ('name', models.CharField(max_length=100, verbose_name='项目名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='项目描述')),
                ('labels', models.TextField(help_text='JSON格式的标签配置', verbose_name='标签配置')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('state', models.IntegerField(default=1, verbose_name='状态')),
            ],
            options={
                'verbose_name': '项目',
                'verbose_name_plural': '项目',
                'db_table': 'xc_project',
            },
        ),
        migrations.AddField(
            model_name='task',
            name='project_code',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='项目编号'),
        ),
    ]
