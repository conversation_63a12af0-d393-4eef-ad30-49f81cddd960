from functools import wraps
from django.shortcuts import render
from app.views.ViewsBase import readUser
import json

# 权限定义
PERMISSIONS = {
    'user_manage': '用户管理',
    'project_manage': '项目管理',
    'task_manage': '任务管理',
    'sample_manage': '样本管理',
    'train_manage': '训练管理',
    'dataset_manage': '数据集管理',
    'storage_manage': '存储管理',
    'annotation_only': '仅标注权限',  # 新增：标注员专用权限
}

# 角色定义
ROLES = {
    'admin': '超级管理员',
    'manager': '管理员',
    'annotator': '标注员',
}

# 角色权限配置
ROLE_PERMISSIONS = {
    'admin': list(PERMISSIONS.keys()),  # 超级管理员拥有所有权限
    'manager': ['project_manage', 'task_manage', 'sample_manage', 'train_manage', 'dataset_manage'],  # 管理员权限
    'annotator': ['annotation_only'],  # 标注员只能标注
}

# 用户权限配置 (username -> permissions list)
USER_PERMISSIONS = {
    # 超级管理员拥有所有权限
    'admin': list(PERMISSIONS.keys()),
    # 普通用户示例
    # 'user1': ['task_manage', 'sample_manage'],
}

# 用户角色配置 (username -> role)
USER_ROLES = {
    'admin': 'admin',
    # 示例：
    # 'annotator1': 'annotator',
    # 'manager1': 'manager',
}

def get_user_role_from_db(username):
    """从数据库获取用户角色"""
    try:
        from app.models import UserRole
        from django.contrib.auth.models import User

        user_obj = User.objects.filter(username=username).first()
        if not user_obj:
            return None

        user_role = UserRole.objects.filter(user=user_obj, is_active=True).first()
        if user_role:
            return user_role.role
        return None
    except Exception as e:
        print(f"获取用户角色失败: {e}")
        return None

def get_user_permissions_from_db(username):
    """从数据库获取用户额外权限"""
    try:
        from app.models import UserRole
        from django.contrib.auth.models import User

        user_obj = User.objects.filter(username=username).first()
        if not user_obj:
            return []

        user_role = UserRole.objects.filter(user=user_obj, is_active=True).first()
        if user_role and user_role.permissions:
            return json.loads(user_role.permissions)
        return []
    except Exception as e:
        print(f"获取用户权限失败: {e}")
        return []

def has_permission(user, permission):
    """检查用户是否有指定权限"""
    if not user:
        return False

    # 超级管理员拥有所有权限
    if user.get('is_superuser'):
        return True

    username = user.get('username', '')

    # 首先从数据库检查用户角色权限
    db_role = get_user_role_from_db(username)
    if db_role:
        role_perms = ROLE_PERMISSIONS.get(db_role, [])
        if permission in role_perms:
            return True

    # 然后从数据库检查用户直接权限配置
    db_perms = get_user_permissions_from_db(username)
    if permission in db_perms:
        return True

    # 兼容性：检查代码中的配置（向后兼容）
    user_role = USER_ROLES.get(username)
    if user_role:
        role_perms = ROLE_PERMISSIONS.get(user_role, [])
        if permission in role_perms:
            return True

    # 检查代码中的直接权限配置
    user_perms = USER_PERMISSIONS.get(username, [])
    return permission in user_perms

def get_user_role(user):
    """获取用户角色"""
    if not user:
        return None

    if user.get('is_superuser'):
        return 'admin'

    username = user.get('username', '')

    # 首先从数据库获取
    db_role = get_user_role_from_db(username)
    if db_role:
        return db_role

    # 兼容性：从代码配置获取
    return USER_ROLES.get(username)

def is_annotator_only(user):
    """检查用户是否为纯标注员"""
    role = get_user_role(user)
    return role == 'annotator'

def require_permission(permission):
    """权限装饰器"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            user = readUser(request)
            if not has_permission(user, permission):
                return render(request, 'app/message.html', {
                    "msg": f"无权限访问，需要权限：{PERMISSIONS.get(permission, permission)}", 
                    "is_success": False, 
                    "redirect_url": "/"
                })
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

def require_superuser(view_func):
    """超级管理员装饰器"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        user = readUser(request)
        if not user or not user.get('is_superuser'):
            return render(request, 'app/message.html', {
                "msg": "需要超级管理员权限", 
                "is_success": False, 
                "redirect_url": "/"
            })
        return view_func(request, *args, **kwargs)
    return wrapper