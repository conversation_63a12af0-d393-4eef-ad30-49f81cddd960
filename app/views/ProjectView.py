import json
import time
from datetime import datetime
from app.views.ViewsBase import *
from app.models import *
from django.shortcuts import render, redirect
from app.utils.Utils import buildPageLabels, gen_random_code_s
from app.permissions import require_permission, has_permission


@require_permission('project_manage')
def index(request):
    """项目列表页面"""
    user = readUser(request)
    if not user:
        return redirect("/login")

    params = parse_get_params(request)
    page = int(params.get("page", 1))
    page_size = int(params.get("page_size", 10))

    skip = (page - 1) * page_size

    # 查询项目列表
    sql_data = "select * from xc_project order by id desc limit %d,%d " % (skip, page_size)
    sql_data_num = "select count(id) as count from xc_project "

    data = g_database.select(sql_data)
    data_num = g_database.select(sql_data_num)

    total = data_num[0]["count"] if data_num else 0

    # 计算总页数
    page_num = (total + page_size - 1) // page_size if total > 0 else 1

    # 构建分页标签
    page_labels = buildPageLabels(page, page_num)

    context = {
        "data": data,
        "page": page,
        "page_size": page_size,
        "page_num": page_num,
        "total": total,
        "page_labels": page_labels,
        "user": user
    }

    return render(request, 'app/project/index.html', context)


@require_permission('project_manage')
def add(request):
    """添加项目"""
    if "POST" == request.method:
        __ret = False
        __msg = "未知错误"

        params = parse_post_params(request)
        handle = params.get("handle")
        code = params.get("code", "").strip()
        name = params.get("name", "").strip()
        description = params.get("description", "").strip()
        labels = params.get("labels", "").strip()

        if "add" == handle and code:
            try:
                if name == "":
                    raise Exception("项目名称不能为空")

                if len(Project.objects.filter(code=code)) > 0:
                    raise Exception("项目编号已经存在")

                # 验证标签JSON格式
                if labels:
                    try:
                        labels_data = json.loads(labels)
                        if not isinstance(labels_data, list):
                            raise Exception("标签配置必须是数组格式")
                        
                        # 验证每个标签的格式
                        for label in labels_data:
                            if not isinstance(label, dict) or 'labelName' not in label:
                                raise Exception("标签格式不正确，必须包含labelName字段")
                    except json.JSONDecodeError:
                        raise Exception("标签配置JSON格式不正确")

                user = readUser(request)
                user_id = user.get("id")
                username = user.get("username")

                project = Project()
                project.user_id = user_id
                project.username = username
                project.code = code
                project.name = name
                project.description = description
                project.labels = labels
                project.state = 1
                project.save()

                __msg = "添加成功"
                __ret = True

            except Exception as e:
                __msg = str(e)
        else:
            __msg = "请求参数不完整"

        res = {
            "code": 1000 if __ret else 0,
            "msg": __msg
        }
        return HttpResponseJson(res)

    else:
        context = {}
        project_code = "proj" + datetime.now().strftime("%Y%m%d%H%M%S")
        context["handle"] = "add"
        context["project"] = {
            "code": project_code,
        }

        return render(request, 'app/project/add.html', context)


@require_permission('project_manage')
def edit(request):
    """编辑项目"""
    if "POST" == request.method:
        __ret = False
        __msg = "未知错误"

        params = parse_post_params(request)
        handle = params.get("handle")
        code = params.get("code", "").strip()
        name = params.get("name", "").strip()
        description = params.get("description", "").strip()
        labels = params.get("labels", "").strip()

        if "edit" == handle and code:
            try:
                project = Project.objects.filter(code=code).first()
                if not project:
                    raise Exception("该项目不存在")

                if name == "":
                    raise Exception("项目名称不能为空")

                # 验证标签JSON格式
                if labels:
                    try:
                        labels_data = json.loads(labels)
                        if not isinstance(labels_data, list):
                            raise Exception("标签配置必须是数组格式")
                        
                        # 验证每个标签的格式
                        for label in labels_data:
                            if not isinstance(label, dict) or 'labelName' not in label:
                                raise Exception("标签格式不正确，必须包含labelName字段")
                    except json.JSONDecodeError:
                        raise Exception("标签配置JSON格式不正确")

                project.name = name
                project.description = description
                project.labels = labels
                project.save()

                __msg = "编辑成功"
                __ret = True

            except Exception as e:
                __msg = str(e)
        else:
            __msg = "请求参数不完整"

        res = {
            "code": 1000 if __ret else 0,
            "msg": __msg
        }
        return HttpResponseJson(res)

    else:
        context = {}
        params = parse_get_params(request)
        code = params.get("code")
        if code:
            project = Project.objects.filter(code=code).first()
            if project:
                context["handle"] = "edit"
                context["project"] = project
            else:
                return render(request, 'app/message.html',
                              {"msg": "请通过项目管理进入", "is_success": False, "redirect_url": "/project/index"})

            return render(request, 'app/project/add.html', context)
        else:
            return redirect("/project/index")


@require_permission('project_manage')
def api_postDel(request):
    """删除项目"""
    ret = False
    msg = "未知错误"
    if request.method == 'POST':
        params = parse_post_params(request)
        code = params.get("code", "").strip()

        if code:
            try:
                # 检查是否有任务关联到此项目
                task_count = Task.objects.filter(project_code=code).count()
                if task_count > 0:
                    raise Exception(f"该项目下还有 {task_count} 个任务，无法删除")

                project = Project.objects.filter(code=code).first()
                if project:
                    project.delete()
                    ret = True
                    msg = "删除成功"
                else:
                    msg = "项目不存在"
            except Exception as e:
                msg = str(e)
        else:
            msg = "项目编号不能为空"
    else:
        msg = "request method not supported"

    res = {
        "code": 1000 if ret else 0,
        "msg": msg
    }
    return HttpResponseJson(res)


def api_getProjectList(request):
    """获取项目列表API（用于任务创建时选择项目）"""
    user = readUser(request)
    if not user:
        res = {"code": 0, "msg": "请先登录"}
        return HttpResponseJson(res)

    try:
        projects = Project.objects.filter(state=1).order_by('-id')
        project_list = []
        for project in projects:
            project_list.append({
                "code": project.code,
                "name": project.name,
                "description": project.description,
                "labels": project.labels
            })

        res = {
            "code": 1000,
            "msg": "success",
            "data": project_list
        }
    except Exception as e:
        res = {
            "code": 0,
            "msg": str(e)
        }

    return HttpResponseJson(res)
