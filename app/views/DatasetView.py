from app.views.ViewsBase import *
from app.models import *
from django.shortcuts import render, redirect
from app.permissions import require_permission


@require_permission('dataset_manage')
def index(request):
    """数据集列表页面"""
    user = readUser(request)
    if not user:
        return redirect("/login")

    context = {
        "user": user,
        "message": "数据集管理功能正在开发中..."
    }

    return render(request, 'app/dataset/index.html', context)


@require_permission('dataset_manage')
def add(request):
    """数据集创建页面"""
    user = readUser(request)
    if not user:
        return redirect("/login")

    context = {
        "user": user,
        "message": "数据集创建功能正在开发中..."
    }

    return render(request, 'app/dataset/add.html', context)
