from app.views.ViewsBase import *
from django.utils.encoding import escape_uri_path
import os
import time
import subprocess
from app.models import *


def download(request):
    params = parse_get_params(request)
    filename = params.get("filename")
    try:
        if filename:
            if filename.endswith(".mp4") \
                    or filename.endswith(".jpg") \
                    or filename.endswith(".jpeg") \
                    or filename.endswith(".png") \
                    or filename.endswith(".pt") \
                    or filename.endswith(".xml") \
                    or filename.endswith(".bin") \
                    or filename.endswith(".csv") \
                    or filename.endswith(".txt") \
                    or filename.endswith(".log") \
                    or filename.endswith(".yaml") \
                    or filename.endswith(".yml") \
                    or filename.endswith(".zip") \
                    or filename.endswith(".xcsettings") \
                    or filename.endswith(".xclogs"):


                    # 对于所有文件类型（包括.pt），使用统一的下载逻辑
                    filepath = os.path.join(g_config.storageDir, filename)

                    if os.path.exists(filepath):
                        f = open(filepath, mode="rb")
                        data = f.read()
                        f.close()

                        # 从完整文件名中提取实际文件名
                        actual_filename = os.path.basename(filename)

                        response = HttpResponse(data, content_type="application/octet-stream")
                        response['Access-Control-Allow-Origin'] = "*"
                        response['Access-Control-Allow-Headers'] = "*"
                        response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                        response['Content-Disposition'] = "attachment;filename={};".format(escape_uri_path(actual_filename))
                        # 不删除训练结果文件，允许重复下载
                        return response
                    else:
                        raise Exception("storage/download filepath not found")



            else:

                raise Exception("storage/download unsupported filename format")
        else:
            raise Exception("storage/download filename not found")

    except Exception as e:
        return HttpResponseJson({"msg": str(e)})

def access(request):
    params = parse_get_params(request)
    filename = params.get("filename")
    try:
        if filename:
            if (filename.endswith(".avi") or filename.endswith(".flv") or filename.endswith(".mp4")
                    or filename.endswith(".jpg") or filename.endswith(".png")  or filename.endswith(".jpeg")
                    or filename.endswith(".pt") or filename.endswith(".bin") or filename.endswith(".xml")
                    or filename.endswith(".txt") or filename.endswith(".log") or filename.endswith(".yaml")
                    or filename.endswith(".yml") or filename.endswith(".csv")):
                filepath = os.path.join(g_config.storageDir, filename)
                if os.path.exists(filepath):
                    f = open(filepath, mode="rb")
                    data = f.read()
                    f.close()

                    if filename.endswith(".mp4"):
                        fs = os.path.getsize(filepath)
                        response = HttpResponse(data, content_type="video/mp4")
                        response['Accept-Ranges'] = "bytes"
                        response['Access-Control-Allow-Origin'] = "*"
                        response['Access-Control-Allow-Headers'] = "*"
                        response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                        response['Content-Length'] = fs
                        # response['Content-Range'] = "bytes 32768-188720863/188720864"
                        # response['Etag'] = "66dc3116-b3fa6e0"
                        # response['Content-Disposition'] = "attachment;filename={};".format(escape_uri_path(filename))
                    elif filename.endswith(".jpg") or filename.endswith(".png") or filename.endswith(".jpeg"):
                        response = HttpResponse(data, content_type="image/jpeg")
                        response['Access-Control-Allow-Origin'] = "*"
                        response['Access-Control-Allow-Headers'] = "*"
                        response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                    elif filename.endswith(".pt") or filename.endswith(".bin") or filename.endswith(".xml"):
                        # 对于模型文件，只返回文件存在的确认，不返回文件内容
                        response = HttpResponseJson({"msg": "file exists", "exists": True})
                        response['Access-Control-Allow-Origin'] = "*"
                        response['Access-Control-Allow-Headers'] = "*"
                        response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                        return response
                    elif filename.endswith(".txt") or filename.endswith(".log") or filename.endswith(".yaml") or filename.endswith(".yml") or filename.endswith(".csv"):
                        # 对于文本文件，返回文本内容供在线查看
                        response = HttpResponse(data, content_type="text/plain; charset=utf-8")
                        response['Access-Control-Allow-Origin'] = "*"
                        response['Access-Control-Allow-Headers'] = "*"
                        response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                    else:
                        response = HttpResponse(data, content_type="application/octet-stream")
                        response['Access-Control-Allow-Origin'] = "*"
                        response['Access-Control-Allow-Headers'] = "*"
                        response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                        response['Content-Disposition'] = "attachment;filename={};".format(escape_uri_path(filename))

                    return response
                else:
                    raise Exception("storage/folder filepath not found")
            else:
                raise Exception("storage/folder unsupported filename format")
        else:
            raise Exception("storage/folder filename not exist")

    except Exception as e:
        return HttpResponseJson({"msg": str(e)})