{% extends "app/base_site.html" %}

{% block title %}项目管理{% endblock %}

{% block content %}
<div class="right_col" role="main">
    <div class="">
        <div class="page-title">
            <div class="title_left">
                <h3>项目管理</h3>
            </div>
        </div>

        <div class="clearfix"></div>

        <div class="row">
            <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="x_panel">
                    <div class="x_title">
                        <h2>项目列表</h2>
                        <div class="clearfix"></div>
                    </div>
                    <div class="x_content">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="card-box table-responsive">
                                    <div class="btn-group" style="margin-bottom: 10px;">
                                        <button onclick="f_add()" class="btn btn-success" type="button">
                                            <i class="fa fa-plus"></i> 添加项目
                                        </button>
                                    </div>

                                    <table id="datatable" class="table table-striped table-bordered" style="width:100%">
                                        <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>项目编号</th>
                                            <th>项目名称</th>
                                            <th>创建用户</th>
                                            <th>标签数量</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                        </thead>

                                        <tbody>
                                        {% for d in data %}
                                        <tr class="even pointer">
                                            <td>{{ d.id }}</td>
                                            <td>{{ d.code }}</td>
                                            <td>{{ d.name }}</td>
                                            <td>{{ d.username }}</td>
                                            <td>
                                                {% if d.labels %}
                                                    {% load project_tags %}
                                                    {{ d.labels|count_labels }}
                                                {% else %}
                                                    0
                                                {% endif %}
                                            </td>
                                            <td>{{ d.create_time|date:"Y-m-d H:i:s" }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button onclick="f_edit('{{ d.code }}')" class="btn btn-sm btn-default" type="button" data-placement="top" data-toggle="tooltip" data-original-title="编辑项目">
                                                        <i class="fa fa-edit"></i>
                                                    </button>
                                                    <button onclick="f_del('{{ d.code }}')" class="btn btn-sm btn-danger" type="button" data-placement="top" data-toggle="tooltip" data-original-title="删除项目">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>

                                    <!-- 分页 -->
                                    <div class="row">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <ul class="pagination">
                                                <li>
                                                    <span style="margin-right:10px;color:#000;">共<span>{{ page_num }}</span>页 / <span>{{ total }}</span>条</span>
                                                </li>

                                                {% for d in page_labels %}
                                                  {% if d.cur == 1 %}
                                                    <li class="paginate_button active"><a href="#">{{ d.name }}</a></li>
                                                  {% else %}
                                                    <li class="paginate_button"><a href="/project/index?page={{ d.page }}&page_size={{ page_size }}">{{ d.name }}</a></li>
                                                  {% endif %}
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function f_add() {
    window.location.href = "/project/add";
}

function f_edit(code) {
    window.location.href = "/project/edit?code=" + code;
}

function f_del(code) {
    if (confirm("确定要删除这个项目吗？")) {
        $.ajax({
            url: "/project/postDel",
            type: "POST",
            data: {
                code: code,
                csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(data) {
                if (data.code == 1000) {
                    alert("删除成功");
                    location.reload();
                } else {
                    alert("删除失败：" + data.msg);
                }
            },
            error: function() {
                alert("网络错误");
            }
        });
    }
}

$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
{% endblock %}
