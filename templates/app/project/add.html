{% extends "app/base_site.html" %}

{% block title %}
{% if handle == "add" %}添加项目{% else %}编辑项目{% endif %}
{% endblock %}

{% block content %}
<div class="right_col" role="main">
    <div class="">
        <div class="page-title">
            <div class="title_left">
                <h3>{% if handle == "add" %}添加项目{% else %}编辑项目{% endif %}</h3>
            </div>
        </div>

        <div class="clearfix"></div>

        <div class="row">
            <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="x_panel">
                    <div class="x_title">
                        <h2>项目信息</h2>
                        <div class="clearfix"></div>
                    </div>
                    <div class="x_content">
                        <br />
                        <form id="demo-form2" data-parsley-validate class="form-horizontal form-label-left">
                            {% csrf_token %}
                            
                            <div class="form-group">
                                <label class="control-label col-md-3 col-sm-3 col-xs-12" for="code">项目编号 <span class="required">*</span></label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <input type="text" id="code" name="code" required="required" class="form-control col-md-7 col-xs-12" value="{{ project.code }}" {% if handle == "edit" %}readonly{% endif %}>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-md-3 col-sm-3 col-xs-12" for="name">项目名称 <span class="required">*</span></label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <input type="text" id="name" name="name" required="required" class="form-control col-md-7 col-xs-12" value="{{ project.name }}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-md-3 col-sm-3 col-xs-12" for="description">项目描述</label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <textarea id="description" name="description" class="form-control" rows="3">{{ project.description }}</textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-md-3 col-sm-3 col-xs-12" for="labels">标签配置</label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <textarea id="labels" name="labels" class="form-control" rows="10" placeholder='请输入JSON格式的标签配置，例如：
[
  {
    "labelName": "person",
    "labelColor": "#ff0000",
    "labelColorR": "255",
    "labelColorG": "0",
    "labelColorB": "0"
  },
  {
    "labelName": "car",
    "labelColor": "#00ff00",
    "labelColorR": "0",
    "labelColorG": "255",
    "labelColorB": "0"
  }
]'>{{ project.labels }}</textarea>
                                    <small class="form-text text-muted">
                                        标签配置为JSON数组格式，每个标签包含labelName（标签名）、labelColor（颜色）、labelColorR/G/B（RGB值）字段
                                    </small>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-md-6 col-sm-6 col-xs-12 col-md-offset-3">
                                    <button type="button" class="btn btn-primary" onclick="f_save()">保存</button>
                                    <button type="button" class="btn btn-default" onclick="f_back()">返回</button>
                                    <button type="button" class="btn btn-info" onclick="f_generateSampleLabels()">生成示例标签</button>
                                    <button type="button" class="btn btn-warning" onclick="f_validateLabels()">验证标签格式</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function f_save() {
    var code = $("#code").val().trim();
    var name = $("#name").val().trim();
    var description = $("#description").val().trim();
    var labels = $("#labels").val().trim();

    if (code == "") {
        alert("项目编号不能为空");
        return;
    }

    if (name == "") {
        alert("项目名称不能为空");
        return;
    }

    // 验证标签JSON格式
    if (labels && !f_validateLabelsFormat(labels)) {
        return;
    }

    $.ajax({
        url: window.location.pathname,
        type: "POST",
        data: {
            handle: "{{ handle }}",
            code: code,
            name: name,
            description: description,
            labels: labels,
            csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
        },
        success: function(data) {
            if (data.code == 1000) {
                alert("保存成功");
                window.location.href = "/project/index";
            } else {
                alert("保存失败：" + data.msg);
            }
        },
        error: function() {
            alert("网络错误");
        }
    });
}

function f_back() {
    window.location.href = "/project/index";
}

function f_generateSampleLabels() {
    var sampleLabels = [
        {
            "labelName": "person",
            "labelColor": "#ff0000",
            "labelColorR": "255",
            "labelColorG": "0",
            "labelColorB": "0"
        },
        {
            "labelName": "car",
            "labelColor": "#00ff00",
            "labelColorR": "0",
            "labelColorG": "255",
            "labelColorB": "0"
        },
        {
            "labelName": "bicycle",
            "labelColor": "#0000ff",
            "labelColorR": "0",
            "labelColorG": "0",
            "labelColorB": "255"
        }
    ];
    
    $("#labels").val(JSON.stringify(sampleLabels, null, 2));
    alert("已生成示例标签配置");
}

function f_validateLabels() {
    var labels = $("#labels").val().trim();
    if (!labels) {
        alert("标签配置为空，验证通过");
        return true;
    }
    
    return f_validateLabelsFormat(labels);
}

function f_validateLabelsFormat(labels) {
    try {
        var labelsData = JSON.parse(labels);
        
        if (!Array.isArray(labelsData)) {
            alert("标签配置必须是数组格式");
            return false;
        }
        
        for (var i = 0; i < labelsData.length; i++) {
            var label = labelsData[i];
            if (typeof label !== 'object' || !label.labelName) {
                alert("第" + (i + 1) + "个标签格式不正确，必须包含labelName字段");
                return false;
            }
        }
        
        alert("标签格式验证通过，共" + labelsData.length + "个标签");
        return true;
    } catch (e) {
        alert("标签配置JSON格式不正确：" + e.message);
        return false;
    }
}
</script>
{% endblock %}
